<!DOCTYPE html>
<html lang="ckb" dir="rtl">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <link rel="icon" type="image/x-icon" href="assets/images/fav.ico" />
    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json" />
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007188" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Quran" />
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png" />
    <!-- Bootstrap CSS and Icons -->
    <link href="assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet" />

    <!-- Bootstrap Icons CSS -->
    <link href="assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />

    <!-- Prism.js CSS for Syntax Highlighting -->
    <link href="assets/vendor/prism/prism.css" rel="stylesheet" />
    <link rel="stylesheet" href="assets/css/style.css" />
    <title>کەوانە بۆ ئایەتەکانی قورئانی پیرۆز</title>
</head>

<body class="bg-light row p-0 m-0">

    <div class="container quran-include col-md-6 text shadow order-2 order-md-1">
        <div id="filter-container" class="px-3 pt-3">
            <div class="row mb-2">
                <div class="col">
                    <label for="chapterSelect" class="text">سورەتێك هەڵبژێرە:</label>
                    <select id="chapterSelect" class="form-select text focus-ring focus-ring-dark border-secondary">
                        <option value="all">هەموو سورەتەکان</option>
                    </select>
                </div>
                <div class="col">
                    <label for="verseSelect" class="text">ئایەتێك هەڵبژێرە:</label>
                    <select id="verseSelect" class="form-select text focus-ring focus-ring-dark border-secondary"
                        disabled>
                        <option value="all">هەموو ئایەتەکان</option>
                    </select>
                </div>
            </div>
            <!-- Input group with reset button on one side and search button on the other -->
            <div class="input-group mb-2">
                <button type="reset" title="Reset" class="btn btn-outline-secondary rounded-0 rounded-end text"
                    id="resetBtn">
                    <i class="bi bi-x-circle"></i>
                </button>
                <input class="form-control rounded-0" type="text" id="searchText" placeholder="لێرەدا بگەڕێ" />
                <button class="btn btn-outline-secondary rounded-0 rounded-start text" id="searchBtn">
                    گەڕان
                </button>
            </div>
            <div class="mb-2 nextPrev d-flex justify-content-start gap-2">
                <button class="btn btn-outline-secondary text col-2" id="prevBtn">
                    پێشتر
                </button>
                <button class="btn btn-outline-secondary text col-2" id="nextBtn">
                    دواتر
                </button>
                <span class="pt-2 text col-2" id="matchCount"></span>
                <span class="pt-2 text col-2" id="chapterNameDisplay"></span>
            </div>
        </div>

        <div id="quranContainer">
            <!-- Chapters will be rendered here -->
        </div>
    </div>

    <div class="container pt-5 col-md-6 order-1 order-md-2">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <form id="quranForm">
                    <div class="card-body">
                        <div class="row g-3">
                            <!-- Ayat input with Info button and Clear button -->
                            <div class="col-12">
                                <div class="input-group">

                                    <input type="checkbox" class="btn-check" id="disableCitation" name="disableCitation"
                                        autocomplete="off" checked>
                                    <label class="btn btn-outline-secondary text pt-2 rounded-0 rounded-end"
                                        for="disableCitation">سەرچاوە</label>
                                    <!-- Ayat input -->
                                    <input id="ayatInput"
                                        class="form-control form-control-lg rounded-0 focus-ring focus-ring-dark"
                                        type="text" name="ayat" placeholder="﷽" required tabindex="1" tabindex="2" />
                                    <!-- Clear button -->
                                    <button title="Clear" class="btn btn-outline-secondary rounded-0 rounded-start"
                                        type="button" id="clearBtn" tabindex="3">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <label for="operator_bahij" class="form-label text">کەوانەکان</label>
                                <select class="form-select sb focus-ring focus-ring-dark" name="operator_bahij"
                                    id="operator_bahij" tabindex="5">
                                    <option value="-">-</option>
                                    <option value="﴿ - ﴾" selected>﴿ - ﴾</option>
                                    <option value="( - )">( - )</option>
                                    <option value="[ - ]">[ - ]</option>
                                    <option value="{ - }">{ - }</option>
                                    <option value="' - '">' - '</option>
                                    <option value='" - "'>" - "</option>
                                    <option value="" - "">" - "</option>
                                    <option value="' - '">' - '</option>
                                    <option value="‹ - ›">‹ - ›</option>
                                    <option value="« - »">« - »</option>
                                    <option value="⦅ - ⦆">⦅ - ⦆</option>
                                    <option value="〚 - 〛">〚 - 〛</option>
                                    <option value="⦃ - ⦄">⦃ - ⦄</option>
                                    <option value="﹙ - ﹚">﹙ - ﹚</option>
                                    <option value="﹛ - ﹜">﹛ - ﹜</option>
                                    <option value="﹝ - ﹞">﹝ - ﹞</option>
                                    <option value="⸨ - ⸩">⸨ - ⸩</option>
                                    <option value="「 - 」">「 - 」</option>
                                    <option value="『 - 』">『 - 』</option>
                                    <option value="〈 - 〉">〈 - 〉</option>
                                    <option value="《 - 》">《 - 》</option>
                                    <option value="【 - 】">【 - 】</option>
                                    <option value="〖 - 〗">〖 - 〗</option>
                                    <option value="〔 - 〕">〔 - 〕</option>
                                    <option value="〘 - 〙">〘 - 〙</option>
                                    <option value="⦗ - ⦘">⦗ - ⦘</option>
                                    <option value="（ - ）">（ - ）</option>
                                    <option value="［ - ］">［ - ］</option>
                                    <option value="｛ - ｝">｛ - ｝</option>
                                    <option value="｟ - ｠">｟ - ｠</option>
                                    <option value="｢ - ｣">｢ - ｣</option>
                                    <option value="❜ - ❛">❜ - ❛</option>
                                    <option value="❞ - ❝">❞ - ❝</option>
                                    <option value="❨ - ❩">❨ - ❩</option>
                                    <option value="❪ - ❫">❪ - ❫</option>
                                    <option value="❴ - ❵">❴ - ❵</option>
                                    <option value="❬ - ❭">❬ - ❭</option>
                                    <option value="❮ - ❯">❮ - ❯</option>
                                    <option value="❰ - ❱">❰ - ❱</option>
                                    <option value="❲ - ❳">❲ - ❳</option>
                                    <option value="᚜ - ᚛">᚜ - ᚛</option>
                                    <option value="༺ - ༻">༺ - ༻</option>
                                    <option value="꧂ - ꧁">꧁ - ꧂</option>
                                </select>
                            </div>
                            <div class="col-6 col-md-3">
                                <label for="operator_hafs" class="form-label text">کەوانە تایبەتەکان</label>
                                <select class="form-select adb focus-ring focus-ring-dark" name="operator_hafs"
                                    id="operator_hafs" tabindex="6">
                                    <option value="">-</option>
                                    <option value="ﵞ - ﵟ">ﵟ - ﵞ</option>
                                    <option value="ﵠ - ﵡ">ﵡ - ﵠ</option>
                                    <option value="ﵢ - ﵣ">ﵣ - ﵢ</option>
                                    <option value="ﵤ - ﵥ">ﵥ - ﵤ</option>
                                    <option value="ﵦ - ﵧ">ﵧ - ﵦ</option>
                                    <option value="ﵨ - ﵩ">ﵩ - ﵨ</option>
                                    <option value="ﵪ - ﵫ">ﵫ - ﵪ</option>
                                    <option value="ﵬ - ﵭ">ﵭ - ﵬ</option>
                                    <option value="ﵮ - ﵯ">ﵯ - ﵮ</option>
                                    <option value="ﵰ - ﵱ">ﵱ - ﵰ</option>
                                    <option value="ﵲ - ﵳ">ﵳ - ﵲ</option>
                                    <option value="ﵴ - ﵵ">ﵵ - ﵴ</option>
                                    <option value="ﵶ - ﵷ">ﵷ - ﵶ</option>
                                    <option value="ﵸ - ﵹ">ﵹ - ﵸ</option>
                                    <option value="ﵺ - ﵻ">ﵻ - ﵺ</option>
                                    <option value="ﵼ - ﵽ">ﵽ - ﵼ</option>
                                </select>
                            </div>
                            <!-- Generate button -->
                            <div class="col-md-6 d-grid">
                                <button type="submit" class="btn btn-secondary mainButton btn-lg text fs-4"
                                    tabindex="7">
                                    <i class="bi bi-magic"></i> دروستکردن
                                </button>
                            </div>
                            <!-- Code output panel -->
                            <div class="col-12">
                                <div class="input-group">
                                    <input aria-label="Code" type="text"
                                        class="form-control border-outline-secondary rounded-0 rounded-end focus-ring focus-ring-dark"
                                        id="codeOutput" readonly />
                                    <button class="btn btn-outline-secondary text rounded-0 rounded-start button"
                                        type="button" id="copyCodeBtn" tabindex="9">
                                        <i class="bi bi-file-code"></i>
                                        <span class="copy-text">کۆپیکردنی کۆد</span>
                                    </button>
                                </div>
                            </div>
                            <!-- Live preview panel -->
                            <div class="col-12">
                                <div class="copy-target position-relative" id="previewPanel">
                                    <span class="copy-badge badge fs-6 text btn btn-outline-secondary button"
                                        tabindex="8">
                                        <i class="bi bi-clipboard"></i>
                                        <span class="copy-text">کۆپیکردن</span>
                                    </span>
                                    <div class="quran-preview text-center" id="livePreview">
                                        <span class="bahij-bracket">﴿</span>
                                        <span class="uthmanic-text">بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ</span>
                                        <span class="bahij-bracket">﴾</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- .row -->
                    </div>
                    <!-- .card-body -->
                </form>
            </div>
            <!-- .col-lg-8 -->
        </div>
        <!-- .row -->
    </div>

    <!-- Info button -->
    <button title="Info" class="btn btn-secondary" type="button" id="infoBtn" tabindex="15">
        <i class="bi bi-info-circle"></i>
    </button>
    <?php include "assets/data/documentation.php"; ?>

    <!-- Bootstrap Bundle with Popper -->
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <!-- Prism.js for Syntax Highlighting -->
    <script src="assets/vendor/prism/prism.js"></script>
    <script src="assets/js/oldapp.js"></script>
</body>

</html>
