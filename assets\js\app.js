// Global variables for Quran functionality
let chaptersData = [];
let searchMatches = [];
let currentMatchIndex = 0;

// PWA variables
let deferredPrompt;
let isOnline = navigator.onLine;

// ---------------------------
// PWA Service Worker Registration
// ---------------------------
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .then((registration) => {
        console.log("SW registered: ", registration);
      })
      .catch((registrationError) => {
        console.log("SW registration failed: ", registrationError);
      });
  });
}

// ---------------------------
// Documentation Functions
// ---------------------------
function copyCssCode(button) {
  const cssCode = document.getElementById("cssCode");
  const text = cssCode.textContent;

  navigator.clipboard
    .writeText(text)
    .then(() => {
      const originalText = button.querySelector(".copy-text").textContent;
      button.querySelector(".copy-text").textContent = "کۆپیکرا!";
      button.classList.add("btn-success");
      button.classList.remove("btn-secondary");

      setTimeout(() => {
        button.querySelector(".copy-text").textContent = originalText;
        button.classList.remove("btn-success");
        button.classList.add("btn-secondary");
      }, 2000);
    })
    .catch((err) => {
      console.error("Failed to copy: ", err);
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);

      const originalText = button.querySelector(".copy-text").textContent;
      button.querySelector(".copy-text").textContent = "کۆپیکرا!";
      setTimeout(() => {
        button.querySelector(".copy-text").textContent = originalText;
      }, 2000);
    });
}

// ---------------------------
// PWA Install Prompt
// ---------------------------
window.addEventListener("beforeinstallprompt", (e) => {
  e.preventDefault();
  deferredPrompt = e;
  showInstallPrompt();
});

function showInstallPrompt() {
  const installPrompt = document.createElement("div");
  installPrompt.className = "install-prompt";
  installPrompt.innerHTML = `
    <div class="install-prompt-content">
      <div class="install-prompt-header">
        <div class="install-prompt-icon">
          <i class="bi bi-download"></i>
        </div>
        <div class="install-prompt-text">
          <h4>دامەزراندنی ئەپ</h4>
          <p>ئەپەکە لەسەر ئامێرەکەت دابمەزرێنە بۆ دەستپێگەیشتنی خێراتر</p>
        </div>
        <button class="install-prompt-close" onclick="dismissInstall()">
          <i class="bi bi-x"></i>
        </button>
      </div>
      <div class="install-prompt-actions">
        <button class="install-btn-primary" onclick="installApp()">
          <i class="bi bi-plus-circle"></i>
          دامەزراندن
        </button>
        <button class="install-btn-secondary" onclick="dismissInstall()">
          دواتر
        </button>
      </div>
    </div>
  `;
  document.body.appendChild(installPrompt);

  // Add animation
  setTimeout(() => {
    installPrompt.classList.add("show");
  }, 100);
}

function installApp() {
  const installPrompt = document.querySelector(".install-prompt");
  if (installPrompt) {
    installPrompt.classList.remove("show");
    setTimeout(() => {
      installPrompt.remove();
    }, 300);
  }

  if (deferredPrompt) {
    deferredPrompt.prompt();
    deferredPrompt.userChoice.then((choiceResult) => {
      if (choiceResult.outcome === "accepted") {
        console.log("User accepted the install prompt");
      }
      deferredPrompt = null;
    });
  }
}

function dismissInstall() {
  const installPrompt = document.querySelector(".install-prompt");
  if (installPrompt) {
    installPrompt.classList.remove("show");
    setTimeout(() => {
      installPrompt.remove();
    }, 300);
  }
}

// ---------------------------
// Online/Offline Status
// ---------------------------
function updateOnlineStatus() {
  const indicator = document.getElementById("offlineIndicator");
  if (navigator.onLine) {
    indicator.textContent = "سەرهێڵ";
    indicator.className = "offline-indicator online";
    isOnline = true;
  } else {
    indicator.textContent = "دەرهێڵ";
    indicator.className = "offline-indicator";
    indicator.style.display = "block";
    isOnline = false;
  }
}

window.addEventListener("online", updateOnlineStatus);
window.addEventListener("offline", updateOnlineStatus);

// ---------------------------
// Common Utility Functions
// ---------------------------

function copyWithFeedback(btn, content) {
  if (btn._copyTimeoutID) {
    clearTimeout(btn._copyTimeoutID);
    const textSpan = btn.querySelector(".copy-text");
    if (textSpan) {
      textSpan.innerText = btn.dataset.originalText || "کۆپیکردن";
    } else {
      btn.innerText = btn.dataset.originalText || "کۆپیکردن";
    }
    btn._copyTimeoutID = null;
  }

  let textSpan = btn.querySelector(".copy-text");
  let originalText = textSpan ? textSpan.innerText : btn.innerText;
  if (!btn.dataset.originalText) {
    btn.dataset.originalText = originalText;
  }

  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        if (textSpan) {
          textSpan.innerText = "کۆپی کرا";
        } else {
          btn.innerText = "کۆپی کرا";
        }
        btn._copyTimeoutID = setTimeout(() => {
          if (textSpan) {
            textSpan.innerText = btn.dataset.originalText;
          } else {
            btn.innerText = btn.dataset.originalText;
          }
          btn._copyTimeoutID = null;
        }, 2000);
      })
      .catch(console.error);
  } else {
    const textArea = document.createElement("textarea");
    textArea.value = content;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      if (textSpan) {
        textSpan.innerText = "کۆپی کرا";
      } else {
        btn.innerText = "کۆپی کرا";
      }
      btn._copyTimeoutID = setTimeout(() => {
        if (textSpan) {
          textSpan.innerText = btn.dataset.originalText;
        } else {
          btn.innerText = btn.dataset.originalText;
        }
        btn._copyTimeoutID = null;
      }, 2000);
    } catch (err) {
      console.error("Fallback error copying:", err);
    }
    document.body.removeChild(textArea);
  }
}

// ---------------------------
// Quran Display Functionality
// ---------------------------

// Helper: Convert number to Arabic numerals
function toArabicNumeral(num) {
  const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
  return String(num)
    .split("")
    .map((d) => arabicDigits[d])
    .join("");
}

// Update the "Verse" dropdown based on the selected chapter.
function updateVerseSelectOptions() {
  const verseSelect = document.getElementById("verseSelect");
  const chapterSelect = document.getElementById("chapterSelect");

  verseSelect.innerHTML = "";
  const defaultOption = document.createElement("option");
  defaultOption.value = "all";
  defaultOption.textContent = "هەموو ئایەتەکان";
  verseSelect.appendChild(defaultOption);

  const selectedChapter = chapterSelect.value;
  if (selectedChapter !== "all") {
    verseSelect.disabled = false;
    const chapter = chaptersData.find((ch) => ch.id == selectedChapter);
    chapter?.verses?.forEach((verse) => {
      const option = document.createElement("option");
      option.value = verse.id;
      option.textContent = toArabicNumeral(verse.id);
      verseSelect.appendChild(option);
    });
  } else {
    verseSelect.disabled = true;
  }
}

// Fetch chapters with offline support
async function fetchChapters() {
  try {
    // Try to get from cache first
    const cachedData = localStorage.getItem("quranData");
    if (cachedData && !isOnline) {
      chaptersData = JSON.parse(cachedData);
      populateChapterSelect();
      renderChapters();
      return;
    }

    // Try multiple possible paths
    const possiblePaths = [
      "/assets/data/quran.json",
      "./assets/data/quran.json",
      "assets/data/quran.json",
      "/bracket.auto/assets/data/quran.json",
    ];

    let response = null;
    let lastError = null;

    for (const path of possiblePaths) {
      try {
        response = await fetch(path);
        if (response.ok) {
          break;
        } else {
          lastError = new Error(
            `HTTP error! status: ${response.status} for path: ${path}`
          );
        }
      } catch (error) {
        lastError = error;
      }
    }

    if (!response || !response.ok) {
      throw lastError || new Error("All paths failed");
    }

    chaptersData = await response.json();

    // Cache the data for offline use
    localStorage.setItem("quranData", JSON.stringify(chaptersData));

    populateChapterSelect();
    renderChapters();
  } catch (error) {
    console.error("Error fetching quran.json:", error);

    // Try to use cached data as fallback
    const cachedData = localStorage.getItem("quranData");
    if (cachedData) {
      chaptersData = JSON.parse(cachedData);
      populateChapterSelect();
      renderChapters();
    } else {
      // Use minimal fallback data
      chaptersData = [
        {
          id: 1,
          name: "الفاتحة",
          transliteration: "Al-Fatihah",
          type: "meccan",
          total_verses: 7,
          verses: [
            { id: 1, text: "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ" },
            { id: 2, text: "ٱلۡحَمۡدُ لِلَّهِ رَبِّ ٱلۡعَٰلَمِينَ" },
            { id: 3, text: "ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ" },
            { id: 4, text: "مَٰلِكِ يَوۡمِ ٱلدِّينِ" },
            { id: 5, text: "إِيَّاكَ نَعۡبُدُ وَإِيَّاكَ نَسۡتَعِينُ" },
            { id: 6, text: "ٱهۡدِنَا ٱلصِّرَٰطَ ٱلۡمُسۡتَقِيمَ" },
            {
              id: 7,
              text: "صِرَٰطَ ٱلَّذِينَ أَنۡعَمۡتَ عَلَيۡهِمۡ غَيۡرِ ٱلۡمَغۡضُوبِ عَلَيۡهِمۡ وَلَا ٱلضَّآلِّينَ",
            },
          ],
        },
      ];

      populateChapterSelect();
      renderChapters();

      // Show warning message to user
      const quranContainer = document.getElementById("quranContainer");
      if (quranContainer) {
        const warningDiv = document.createElement("div");
        warningDiv.className = "alert alert-warning mb-3";
        warningDiv.innerHTML =
          "⚠️ تەنها سورەتی فاتیحە لەبەردەستدایە. بۆ بینینی هەموو قورئان، ئینتەرنێت پێویستە.";
        quranContainer.insertBefore(warningDiv, quranContainer.firstChild);
      }
    }
  }
}

function populateChapterSelect() {
  const chapterSelect = document.getElementById("chapterSelect");

  if (!chapterSelect) {
    return;
  }

  chapterSelect.innerHTML = ""; // Clear existing options

  // Add the "All Chapters" option
  const allOption = document.createElement("option");
  allOption.value = "all";
  allOption.textContent = "هەموو سورەتەکان";
  chapterSelect.appendChild(allOption);

  // Add individual chapters
  chaptersData.forEach((chapter) => {
    const option = document.createElement("option");
    option.value = chapter.id;
    option.textContent = "سورة " + chapter.name;
    chapterSelect.appendChild(option);
  });
}

function renderChapters() {
  const quranContainer = document.getElementById("quranContainer");
  const chapterSelect = document.getElementById("chapterSelect");

  if (!quranContainer || !chapterSelect) {
    return;
  }

  quranContainer.innerHTML = "";

  const selectedChapter = chapterSelect.value;
  chaptersData.forEach((chapter) => {
    if (selectedChapter !== "all" && chapter.id != selectedChapter) return;

    const chapterDiv = document.createElement("div");
    chapterDiv.classList.add("chapter");
    chapterDiv.dataset.chapterId = chapter.id;

    const header = document.createElement("h2");
    header.textContent = "سورة " + chapter.name;
    chapterDiv.appendChild(header);

    const p = document.createElement("p");
    if (chapter.verses?.length) {
      chapter.verses.forEach((verse) => {
        const verseSpan = document.createElement("span");
        verseSpan.classList.add("verse");
        verseSpan.dataset.verseId = verse.id;
        verseSpan.textContent = `${verse.text} ${toArabicNumeral(verse.id)}`;
        p.appendChild(verseSpan);
        p.appendChild(document.createTextNode(" "));
      });
    }
    chapterDiv.appendChild(p);
    quranContainer.appendChild(chapterDiv);
  });
  updateVerseSelectOptions();
}

// ---------------------------
// Event Listeners and UI Setup
// ---------------------------
document.addEventListener("DOMContentLoaded", async () => {
  // Create offline indicator
  const offlineIndicator = document.createElement("div");
  offlineIndicator.id = "offlineIndicator";
  offlineIndicator.className = "offline-indicator";
  document.body.appendChild(offlineIndicator);
  updateOnlineStatus();

  // Common DOM Elements
  const form = document.getElementById("quranForm");
  const livePreview = document.getElementById("livePreview");
  const codeOutput = document.getElementById("codeOutput");
  const previewPanel = document.getElementById("previewPanel");
  const copyCodeBtn = document.getElementById("copyCodeBtn");
  const clearBtn = document.getElementById("clearBtn");
  const infoBtn = document.getElementById("infoBtn");
  const ayatInput = document.getElementById("ayatInput");

  // Quran-related DOM Elements
  const chapterSelect = document.getElementById("chapterSelect");
  const verseSelect = document.getElementById("verseSelect");
  const searchText = document.getElementById("searchText");
  const searchBtn = document.getElementById("searchBtn");
  const resetBtn = document.getElementById("resetBtn");
  const prevBtn = document.getElementById("prevBtn");
  const nextBtn = document.getElementById("nextBtn");

  // ---------------------------
  // Form and Preview Functionality with Offline Support
  // ---------------------------
  // Auto-submit form when citation checkbox changes
  document
    .getElementById("disableCitation")
    ?.addEventListener("change", function () {
      form?.dispatchEvent(new Event("submit")); // Submit the form automatically
    });

  form?.addEventListener("submit", async (e) => {
    e.preventDefault();

    if (!isOnline) {
      // Offline bracket generation
      generateBracketsOffline();
      return;
    }

    const formData = new FormData(form);
    formData.append(
      "disableCitation",
      document.getElementById("disableCitation").checked ? "0" : "1"
    );
    const params = new URLSearchParams(formData);

    try {
      const response = await fetch(`./api/generate.php?${params}`);
      const { html } = await response.json();
      livePreview.innerHTML = html;
      codeOutput.value = html;
    } catch (error) {
      console.error("Error:", error);
      // Fallback to offline generation
      generateBracketsOffline();
    }
  });

  // Function to normalize Arabic text for matching
  function normalizeArabic(text) {
    // Remove diacritics and normalize
    return text
      .replace(/[\u064B-\u065F\u0670\u06D6-\u06ED]/g, "") // Remove diacritics
      .replace(/[\u0622\u0623\u0625\u0671]/g, "ا") // Normalize Alif
      .replace(/\u0629/g, "ه") // Normalize Teh Marbuta
      .replace(/\u0640/g, "") // Remove Tatweel
      .replace(/\s+/g, " ") // Normalize spaces
      .trim();
  }

  // Function to convert number to Arabic numerals
  function toArabicNumeral(num) {
    const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
    return num
      .toString()
      .split("")
      .map((digit) => arabicDigits[parseInt(digit)])
      .join("");
  }

  // Function to build chapter display text with boundaries (matching PHP logic)
  function buildChapterDisplay(chapter) {
    const displayParts = [];
    const boundaries = [];
    let cumulative = 0;

    for (const verse of chapter.verses) {
      // Build display text for the verse exactly as rendered
      const verseDisplay =
        unifySpaces(verse.text) + " " + toArabicNumeral(verse.id);
      displayParts.push(verseDisplay);
      cumulative += verseDisplay.length + 1; // +1 for the space between verses
      boundaries.push(cumulative);
    }

    // Join all parts with a single space
    const chapterDisplay = displayParts.join(" ");
    return [chapterDisplay, boundaries];
  }

  // Function to find citation for text in offline mode
  function findCitationForText(text) {
    if (!chaptersData || chaptersData.length === 0) return "";

    const selectedText = unifySpaces(text);
    if (!selectedText.trim()) return "";

    // Search through all chapters to find the exact text sequence
    for (const chapter of chaptersData) {
      const [chapterDisplay, boundaries] = buildChapterDisplay(chapter);

      // Find the selected text in the chapter display
      const startIndex = chapterDisplay
        .toLowerCase()
        .indexOf(selectedText.toLowerCase());
      if (startIndex === -1) {
        continue; // Try next chapter
      }

      const endIndex = startIndex + selectedText.length;

      // Determine the verse number corresponding to the start and end index
      let startVerse = null;
      let endVerse = null;

      for (let i = 0; i < boundaries.length; i++) {
        if (startVerse === null && startIndex < boundaries[i]) {
          startVerse = chapter.verses[i].id;
        }
        if (endIndex <= boundaries[i]) {
          endVerse = chapter.verses[i].id;
          break;
        }
      }

      // If endVerse not found, use the last verse
      if (endVerse === null) {
        endVerse = chapter.verses[chapter.verses.length - 1].id;
      }

      // Build citation
      if (startVerse === endVerse) {
        return ` (${chapter.name}: ${startVerse})`;
      } else {
        return ` (${chapter.name}: ${startVerse}-${endVerse})`;
      }
    }

    return ""; // Text not found in Quran
  }

  // Offline bracket generation function
  function generateBracketsOffline() {
    const ayat = ayatInput.value.trim();
    if (!ayat) return;

    const operatorBahij = document.getElementById("operator_bahij").value;
    const operatorHafs = document.getElementById("operator_hafs").value;
    const operator = operatorHafs || operatorBahij;
    const disableCitation = !document.getElementById("disableCitation").checked;

    // Bracket pairs mapping
    const brackets = {
      "﴿ - ﴾": ["﴿", "﴾"],
      "( - )": ["(", ")"],
      "[ - ]": ["[", "]"],
      "{ - }": ["{", "}"],
      "-": ["", ""],
      "' - '": ["'", "'"],
      '" - "': ['"', '"'],
      '" - "': ['"', '"'],
      "' - '": ["'", "'"],
      "‹ - ›": ["‹", "›"],
      "« - »": ["«", "»"],
      "⦅ - ⦆": ["⦅", "⦆"],
      "〚 - 〛": ["〚", "〛"],
      "⦃ - ⦄": ["⦃", "⦄"],
      "﹙ - ﹚": ["﹙", "﹚"],
      "﹛ - ﹜": ["﹛", "﹜"],
      "﹝ - ﹞": ["﹝", "﹞"],
      "⸨ - ⸩": ["⸨", "⸩"],
      "「 - 」": ["「", "」"],
      "『 - 』": ["『", "』"],
      "〈 - 〉": ["〈", "〉"],
      "《 - 》": ["《", "》"],
      "【 - 】": ["【", "】"],
      "〖 - 〗": ["〖", "〗"],
      "〔 - 〕": ["〔", "〕"],
      "〘 - 〙": ["〘", "〙"],
      "⦗ - ⦘": ["⦗", "⦘"],
      "（ - ）": ["（", "）"],
      "［ - ］": ["［", "］"],
      "｛ - ｝": ["｛", "｝"],
      "｟ - ｠": ["｟", "｠"],
      "｢ - ｣": ["｢", "｣"],
      "❜ - ❛": ["❜", "❛"],
      "❞ - ❝": ["❞", "❝"],
      "❨ - ❩": ["❨", "❩"],
      "❪ - ❫": ["❪", "❫"],
      "❴ - ❵": ["❴", "❵"],
      "❬ - ❭": ["❬", "❭"],
      "❮ - ❯": ["❮", "❯"],
      "❰ - ❱": ["❰", "❱"],
      "❲ - ❳": ["❲", "❳"],
      "᚜ - ᚛": ["᚜", "᚛"],
      "༺ - ༻": ["༺", "༻"],
      "꧂ - ꧁": ["꧂", "꧁"],
      // Uthmanic Hafs bracket pairs
      "ﵞ - ﵟ": ["ﵟ", "ﵞ"],
      "ﵠ - ﵡ": ["ﵡ", "ﵠ"],
      "ﵢ - ﵣ": ["ﵣ", "ﵢ"],
      "ﵤ - ﵥ": ["ﵥ", "ﵤ"],
      "ﵦ - ﵧ": ["ﵧ", "ﵦ"],
      "ﵨ - ﵩ": ["ﵩ", "ﵨ"],
      "ﵪ - ﵫ": ["ﵫ", "ﵪ"],
      "ﵬ - ﵭ": ["ﵭ", "ﵬ"],
      "ﵮ - ﵯ": ["ﵯ", "ﵮ"],
      "ﵰ - ﵱ": ["ﵱ", "ﵰ"],
      "ﵲ - ﵳ": ["ﵳ", "ﵲ"],
      "ﵴ - ﵵ": ["ﵵ", "ﵴ"],
      "ﵶ - ﵷ": ["ﵷ", "ﵶ"],
      "ﵸ - ﵹ": ["ﵹ", "ﵸ"],
      "ﵺ - ﵻ": ["ﵻ", "ﵺ"],
      "ﵼ - ﵽ": ["ﵽ", "ﵼ"],
    };

    const uthmanicOperators = [
      "ﵞ - ﵟ",
      "ﵠ - ﵡ",
      "ﵢ - ﵣ",
      "ﵤ - ﵥ",
      "ﵦ - ﵧ",
      "ﵨ - ﵩ",
      "ﵪ - ﵫ",
      "ﵬ - ﵭ",
      "ﵮ - ﵯ",
      "ﵰ - ﵱ",
      "ﵲ - ﵳ",
      "ﵴ - ﵵ",
      "ﵶ - ﵷ",
      "ﵸ - ﵹ",
      "ﵺ - ﵻ",
      "ﵼ - ﵽ",
    ];

    const bracketClass = uthmanicOperators.includes(operator)
      ? "uthmanic-text"
      : "bahij-bracket";
    const [open, close] = brackets[operator] || brackets["﴿ - ﴾"];

    // Add citation if enabled and text is found in Quran data
    let citation = "";
    if (!disableCitation && chaptersData.length > 0) {
      citation = findCitationForText(ayat);
    }

    const html = `<span class="${bracketClass}">${open}</span><span class="uthmanic-text">${ayat}</span><span class="${bracketClass}">${close}</span>${citation}`;

    livePreview.innerHTML = html;
    codeOutput.value = html;
  }

  previewPanel?.addEventListener("click", function () {
    const text = livePreview.textContent;
    if (text) {
      const badge = this.querySelector(".copy-badge");
      copyWithFeedback(badge, text);
    }
  });

  copyCodeBtn?.addEventListener("click", function () {
    if (codeOutput.value) {
      copyWithFeedback(this, codeOutput.value);
    }
  });

  // Initialize Quran functionality
  try {
    await fetchChapters();
  } catch (error) {
    console.error("Failed to load Quran data:", error);
  }

  // Add default text to input if empty
  if (ayatInput && !ayatInput.value.trim()) {
    ayatInput.placeholder = "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ";
  }

  // ---------------------------
  // Text Insertion Functionality
  // ---------------------------
  const navContainer =
    document.querySelector(".quran-container .nextPrev") ||
    document.querySelector(".quran-include .nextPrev");
  if (navContainer) {
    const insertButton = document.createElement("button");
    insertButton.innerHTML = 'دەق بنێرە <i class="bi bi-arrow-bar-left"></i>';
    insertButton.id = "insertTextButton";
    insertButton.classList.add(
      "btn",
      "btn-outline-secondary",
      "mainButton",
      "float-start",
      "col-3",
      "text-light",
      "shadow"
    );
    insertButton.style.display = "none";
    navContainer.appendChild(insertButton);

    let selectedText = "";
    document.addEventListener("mouseup", () => {
      selectedText = window.getSelection().toString().trim();
      insertButton.style.display = selectedText ? "inline-block" : "none";
    });

    insertButton.addEventListener("click", () => {
      if (ayatInput) {
        ayatInput.value = selectedText;
        insertButton.style.display = "none";
        // Trigger the "دروستکردن" (Generate) button.
        const generateButton =
          document.querySelector("#generateButton") ||
          document.querySelector(".generateButton");
        if (generateButton) {
          generateButton.click();
        } else {
          // Fallback: trigger the first .mainButton that is not the insert button.
          const mainButtons = document.querySelectorAll(".mainButton");
          for (let btn of mainButtons) {
            if (btn.id !== "insertTextButton") {
              btn.click();
              break;
            }
          }
        }
      }
    });
  }

  // ---------------------------
  // Event Listeners for Navigation and Search
  // ---------------------------
  chapterSelect?.addEventListener("change", () => {
    renderChapters();
    updateVerseSelectOptions();
    clearSearchHighlights();
  });

  verseSelect?.addEventListener("change", jumpToVerse);
  searchBtn?.addEventListener("click", performSearch);
  nextBtn?.addEventListener("click", nextMatch);
  prevBtn?.addEventListener("click", prevMatch);
  searchText?.addEventListener(
    "keyup",
    (e) => e.key === "Enter" && performSearch()
  );
  resetBtn?.addEventListener("click", resetSearchAndFilters);

  // Clear button: reset ayat input, bracket dropdowns, Quran preview, and code output.
  clearBtn?.addEventListener("click", () => {
    // Clear the input field.
    if (ayatInput) ayatInput.value = "";

    // Reset the "کەوانەکان" dropdown (operator_bahij) to its default value.
    const operatorBahij = document.getElementById("operator_bahij");
    if (operatorBahij) {
      operatorBahij.value = "﴿ - ﴾"; // Default value as set in the HTML
    }

    // Reset the "کەوانە تایبەتەکان" dropdown (operator_hafs) to its default value.
    const operatorHafs = document.getElementById("operator_hafs");
    if (operatorHafs) {
      operatorHafs.value = ""; // Default empty value
    }

    // Reset the Quran preview to its default content.
    const livePreview = document.getElementById("livePreview");
    if (livePreview) {
      livePreview.innerHTML = `
      <span class="bahij-bracket">﴿</span>
      <span class="uthmanic-text">بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ</span>
      <span class="bahij-bracket">﴾</span>
    `;
    }

    // Reset the code output field.
    const codeOutput = document.getElementById("codeOutput");
    if (codeOutput) {
      codeOutput.value = ""; // Clear the generated code
    }
  });

  infoBtn?.addEventListener("click", () => {
    const modalElement = document.getElementById("infoModal");
    if (modalElement) {
      if (typeof bootstrap !== "undefined" && bootstrap.Modal) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
      } else {
        // Fallback: show modal manually
        modalElement.style.display = "block";
        modalElement.classList.add("show");
        document.body.classList.add("modal-open");

        // Add backdrop
        const backdrop = document.createElement("div");
        backdrop.className = "modal-backdrop fade show";
        document.body.appendChild(backdrop);

        // Close modal when clicking close button or backdrop
        const closeModal = () => {
          modalElement.style.display = "none";
          modalElement.classList.remove("show");
          document.body.classList.remove("modal-open");
          if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
          }
        };

        modalElement
          .querySelector('[data-bs-dismiss="modal"]')
          ?.addEventListener("click", closeModal);
        backdrop.addEventListener("click", closeModal);
      }
    }
  });
});

// ---------------------------
// Quran Search Functions
// ---------------------------
function jumpToVerse() {
  const verseSelect = document.getElementById("verseSelect");
  const selectedVerse = verseSelect.value;
  if (selectedVerse === "all") return;
  const verseSpan = document.querySelector(
    `.chapter:not([style*="display: none"]) .verse[data-verse-id="${selectedVerse}"]`
  );
  if (verseSpan) {
    verseSpan.scrollIntoView({ behavior: "smooth", block: "center" });
    clearActiveHighlights();
    verseSpan.classList.add("active");
  }
}

function clearActiveHighlights() {
  document.querySelectorAll(".verse").forEach((span) => {
    span.classList.remove("active");
  });
}

function clearSearchHighlights() {
  document.querySelectorAll(".verse").forEach((span) => {
    span.classList.remove("highlight", "active");
  });
  searchMatches = [];
  currentMatchIndex = 0;
  const matchCountElem = document.getElementById("matchCount");
  if (matchCountElem) {
    matchCountElem.textContent = "";
  }
}

function normalizeArabic(str) {
  return (
    str
      // Decompose combined characters into base letters + diacritics.
      .normalize("NFD")
      // Remove diacritical marks and other extra symbols:
      // \u0640: Tatweel
      // \u064B-\u065F: All harakat and related marks
      // \u0670: Additional combining mark for Alef
      // \u06D6-\u06ED: Quranic-specific marks
      .replace(/[\u0640\u064B-\u065F\u0670\u06D6-\u06ED]/g, "")
      // Normalize alif and hamza variants to a standard "ا".
      .replace(/[\u0621\u0622\u0623\u0625\u0671]/g, "ا")
      // Collapse multiple "ا" into one (if needed).
      .replace(/ا{2,}/g, "ا")
      .trim()
  );
}

function precomputeNormalizedText(chapters) {
  chapters.forEach((chapter) => {
    chapter.verses.forEach((verse) => {
      verse.normalizedText = normalizeArabic(verse.text);
    });
  });
}

function performSearch() {
  clearSearchHighlights();
  const searchInput = document.getElementById("searchText").value.trim();
  if (!searchInput) return;

  const normalizedSearch = normalizeArabic(searchInput);

  const allVerses = document.querySelectorAll(".verse");
  searchMatches = [];

  allVerses.forEach((span) => {
    const normText = span.dataset.norm || normalizeArabic(span.textContent);
    if (normText.includes(normalizedSearch)) {
      span.classList.add("highlight");
      searchMatches.push(span);
    }
  });

  if (searchMatches.length) {
    currentMatchIndex = 0;
    activateCurrentMatch();
  } else {
    const matchCountElem = document.getElementById("matchCount");
    if (matchCountElem) {
      matchCountElem.textContent = "هیچ نەدۆزرایەوە";
    }
  }
}

function activateCurrentMatch() {
  clearActiveHighlights();
  if (!searchMatches.length) return;

  const currentSpan = searchMatches[currentMatchIndex];
  currentSpan.classList.add("active");
  currentSpan.scrollIntoView({ behavior: "smooth", block: "center" });

  // Update match count display
  const matchCountElem = document.getElementById("matchCount");
  if (matchCountElem) {
    matchCountElem.textContent = `${currentMatchIndex + 1} لە ${
      searchMatches.length
    }`;
  }

  // Update chapter name display
  const chapterNameElem = document.getElementById("chapterNameDisplay");
  if (chapterNameElem) {
    const chapterDiv = currentSpan.closest(".chapter");
    if (chapterDiv) {
      const header = chapterDiv.querySelector("h2");
      chapterNameElem.textContent = header ? header.textContent : "";
    } else {
      chapterNameElem.textContent = "";
    }
  }
}

function nextMatch() {
  if (searchMatches.length) {
    currentMatchIndex = (currentMatchIndex + 1) % searchMatches.length;
    activateCurrentMatch();
  }
}

function prevMatch() {
  if (searchMatches.length) {
    currentMatchIndex =
      (currentMatchIndex - 1 + searchMatches.length) % searchMatches.length;
    activateCurrentMatch();
  }
}

function resetSearchAndFilters() {
  const searchText = document.getElementById("searchText");
  if (searchText) searchText.value = "";

  const chapterSelect = document.getElementById("chapterSelect");
  if (chapterSelect) chapterSelect.value = "all";

  const chapterNameDisplay = document.getElementById("chapterNameDisplay");
  if (chapterNameDisplay) chapterNameDisplay.innerText = "";

  clearSearchHighlights();
  renderChapters();

  const insertButton = document.getElementById("insertTextButton");
  if (insertButton) {
    insertButton.style.display = "none";
  }

  if (window.getSelection) {
    window.getSelection().removeAllRanges();
  }

  // Scroll only inside `#quranContainer` smoothly
  const quranContainer = document.getElementById("quranContainer");
  if (quranContainer) {
    quranContainer.scrollTo({ top: 0, behavior: "smooth" });
  }
}

// ---------------------------
// CSS Copy Functionality
// ---------------------------
function copyCssCode(btn) {
  const codeElement = document.getElementById("cssCode");
  copyWithFeedback(btn, codeElement?.innerText?.trim() || "");
}

// Make functions globally available
window.installApp = installApp;
window.dismissInstall = dismissInstall;
window.copyCssCode = copyCssCode;
